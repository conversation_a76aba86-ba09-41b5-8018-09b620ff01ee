# Dynamické pozadia pre Kapitolu 3: Pátranie v zámku

## Prehľad implementácie

Kapitola 3 teraz používa dynamické pozadia z priečinka `assets/pozadia/Kapitola_3/` podľa logiky príbehu a dialógov.

## Logika pozadí

### **Story Phase 0 (Úvod)**
- **Pozadie**: `1.png` 
- **Kontext**: "Vstupujete do veľkej haly osvetlenej len plamenným mihotaním krbu"
- **Dialógy**: Úvodné dialógy s Viktorom o zmiznutí Van Helsinga

### **Story Phase 1 (Po úvodných dialógoch)**
- **Pozadie**: `2.png` (zmena pred prvou hádankou)
- **Kontext**: Zobrazenie prvého hlavolamu (Obrátená správa)
- **Akcia**: Prvý hlavolam sa zobrazí

### **Story Phase 2 (Po prvom puzzle)**
- **Pozadie**: `3.png` (zmena pri vete "Vstupujete do rozľahlej knižnice")
- **Kontext**: Interlude dialógy s Viktorom, vstup do knižnice
- **Dialógy**: "Vonkajšie múry! Samozrejme!" → "Vstupujete do rozľahlej knižnice"

### **Story Phase 3 (Po interlude dialógoch)**
- **Pozadie**: `3.png` (zostáva v knižnici)
- **Kontext**: Pokračovanie pátrania v knižnici
- **Akcia**: Zobrazenie druhého hlavolamu (Jednoduchý výpočet)

### **Počas puzzle**
- **Pozadie**: `UI_Pozadie.png` (štandardné pozadie pre všetky hlavolamy)

## Implementované zmeny

### **1. scenes/Chapter3.tscn**
```
[ext_resource type="Texture2D" path="res://assets/pozadia/Kapitola_3/1.png" id="2_bg"]
```
- Zmenené z `UI_Pozadie.png` na `1.png`
- Počiatočné pozadie je teraz prvý obrázok z Kapitola_3

### **2. scripts/Chapter.gd**

**Zmena pozadia po úvodných dialógoch:**
```gdscript
# Po úvodných dialógoch - ukázať prvý hlavolam
story_phase = 1
# Pre Kapitolu 3 - zmeniť pozadie na druhý obrázok (pred prvou hádankou)
elif chapter_number == 3:
    change_background_image("res://assets/pozadia/Kapitola_3/2.png")
```

**Zmena pozadia počas interlude dialógov:**
```gdscript
# V DialogueSystem.gd - detekcia konkrétnej vety
if text == "Vstupujete do rozľahlej knižnice. Regály siahajú až k stropu.":
    background_change_requested.emit("res://assets/pozadia/Kapitola_3/3.png")

# V Chapter.gd - spracovanie signálu
func _on_background_change_requested(image_path: String):
    change_background_image(image_path)
```

**Po interlude dialógoch:**
```gdscript
# Po dialógoch medzi hlavolamami - ukázať druhý hlavolam
story_phase = 3
# Pre Kapitolu 3 - pozadie už je zmenené na 3.png pri vete o knižnici
# Žiadna dodatočná zmena pozadia nie je potrebná
```

**Pozadie počas puzzle:**
```gdscript
func show_puzzle_scene(puzzle_number: int):
    # Pri spustení puzzle vrátiť pozadie na UI_Pozadie.png
    change_background_image("res://assets/Obrázky/UI_Pozadie.png")
```

**Spustenie interlude dialógov po prvom puzzle:**
```gdscript
func _on_reversed_message_solved():
    # Pre kapitolu 3 - pokračovať v príbehu
    if chapter_number == 3:
        story_phase = 2
        hide_puzzle_buttons()
        # Spustiť interlude dialógy (pozadie sa zmení automaticky pri konkrétnej vete)
        if dialogue_system:
            var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
            dialogue_system.start_dialogue(interlude_dialogue)
```

**Po druhom puzzle:**
```gdscript
func _on_simple_calculation_solved():
    # Pre kapitolu 3 - dokončiť kapitolu
    if chapter_number == 3:
        # Pozadie už je na 3.png, žiadna zmena nie je potrebná
        complete_puzzle(2)
```

## Súhrn zmien pozadí

| Fáza príbehu | Pozadie | Popis |
|--------------|---------|-------|
| Story Phase 0 | `1.png` | Veľká hala s krbom |
| Story Phase 1 | `2.png` | Po úvodných dialógoch (pred prvou hádankou) |
| Story Phase 2 | `3.png` | Pri vete "Vstupujete do rozľahlej knižnice" |
| Story Phase 3 | `3.png` | Zostáva v knižnici |
| Počas puzzle | `UI_Pozadie.png` | Štandardné pozadie |

## Testovanie

Pre testovanie dynamických pozadí:
1. Spustite Kapitolu 3
2. Prejdite cez úvodné dialógy - pozadie sa zmení na knižnicu
3. Vyriešte prvý hlavolam - pozadie sa vráti na knižnicu
4. Prejdite cez interlude dialógy - pozadie sa zmení na finálny obrázok knižnice
5. Vyriešte druhý hlavolam - pozadie zostane na finálnom obrázku

## Automatický prechod do kapitoly 4

Kapitola 3 má implementované záverečné dialógy, ktoré sa spustia po dokončení druhého hlavolamu:
- Po vyriešení "Jednoduchý výpočet" sa spustia záverečné dialógy s Van Helsingovým denníkom
- Po dokončení záverečných dialógov sa automaticky zobrazí dialóg dokončenia kapitoly
- Po 3 sekundách sa automaticky načíta Kapitola 4

## Poznámky

- Implementácia sleduje rovnakú logiku ako Kapitoly 1 a 2
- Pozadia sa menia podľa naratívneho toku príbehu a konkrétnych viet v dialógoch
- Puzzle používajú štandardné pozadie `UI_Pozadie.png`
- Zmeny sú konzistentné s existujúcim systémom dynamických pozadí
- Zmena pozadia na knižnicu sa spúšťa presne pri vete "Vstupujete do rozľahlej knižnice"
