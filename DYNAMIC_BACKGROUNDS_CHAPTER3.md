# Dynamické pozadia pre Kapitolu 3: Pátranie v zámku

## Prehľad implementácie

Kapitola 3 teraz používa dynamické pozadia z priečinka `assets/pozadia/Kapitola_3/` podľa logiky príbehu a dialógov.

## Logika pozadí

### **Story Phase 0 (Úvod)**
- **Pozadie**: `1.png` 
- **Kontext**: "Vstupujete do veľkej haly osvetlenej len plamenným mihotaním krbu"
- **Dialógy**: Úvodné dialógy s Viktorom o zmiznutí Van Helsinga

### **Story Phase 1 (Po úvodných dialógoch)**
- **Pozadie**: `2.png`
- **Kontext**: "Vstupujete do rozľahlej knižnice. Regály siahajú až k stropu"
- **Akcia**: Zobrazenie prvého hlavolamu (Obrátená správa)

### **Story Phase 2 (Po prvom puzzle)**
- **Pozadie**: `2.png` (zostáva v knižnici)
- **Kontext**: Pokračovanie v knižnici, interlude dialógy s Viktorom
- **Dialógy**: "Vonkajšie múry! Samozrejme!"

### **Story Phase 3 (Po interlude dialógoch)**
- **Pozadie**: `3.png`
- **Kontext**: Pokračovanie pátrania v knižnici
- **Akcia**: Zobrazenie druhého hlavolamu (Jednoduchý výpočet)

### **Počas puzzle**
- **Pozadie**: `UI_Pozadie.png` (štandardné pozadie pre všetky hlavolamy)

## Implementované zmeny

### **1. scenes/Chapter3.tscn**
```
[ext_resource type="Texture2D" path="res://assets/pozadia/Kapitola_3/1.png" id="2_bg"]
```
- Zmenené z `UI_Pozadie.png` na `1.png`
- Počiatočné pozadie je teraz prvý obrázok z Kapitola_3

### **2. scripts/Chapter.gd**

**Zmena pozadia po úvodných dialógoch:**
```gdscript
# Po úvodných dialógoch - ukázať prvý hlavolam
story_phase = 1
# Pre Kapitolu 3 - zmeniť pozadie na druhý obrázok (knižnica)
elif chapter_number == 3:
    change_background_image("res://assets/pozadia/Kapitola_3/2.png")
```

**Zmena pozadia po interlude dialógoch:**
```gdscript
# Po dialógoch medzi hlavolamami - ukázať druhý hlavolam
story_phase = 3
# Pre Kapitolu 3 - zmeniť pozadie na tretí obrázok (pokračovanie v knižnici)
elif chapter_number == 3:
    change_background_image("res://assets/pozadia/Kapitola_3/3.png")
```

**Pozadie počas puzzle:**
```gdscript
func show_puzzle_scene(puzzle_number: int):
    # Pri spustení puzzle vrátiť pozadie na UI_Pozadie.png
    change_background_image("res://assets/Obrázky/UI_Pozadie.png")
```

**Návrat pozadia po prvom puzzle:**
```gdscript
func _on_reversed_message_solved():
    # Pre kapitolu 3 - pokračovať v príbehu
    if chapter_number == 3:
        story_phase = 2
        hide_puzzle_buttons()
        # Vrátiť pozadie na druhý obrázok (po prvom puzzle - knižnica)
        change_background_image("res://assets/pozadia/Kapitola_3/2.png")
```

**Návrat pozadia po druhom puzzle:**
```gdscript
func _on_simple_calculation_solved():
    # Pre kapitolu 3 - dokončiť kapitolu
    if chapter_number == 3:
        # Vrátiť pozadie na tretí obrázok (po druhom puzzle)
        change_background_image("res://assets/pozadia/Kapitola_3/3.png")
        complete_puzzle(2)
```

## Súhrn zmien pozadí

| Fáza príbehu | Pozadie | Popis |
|--------------|---------|-------|
| Story Phase 0 | `1.png` | Veľká hala s krbom |
| Story Phase 1 | `2.png` | Knižnica (po úvodných dialógoch) |
| Story Phase 2 | `2.png` | Knižnica (po prvom puzzle) |
| Story Phase 3 | `3.png` | Knižnica (po interlude dialógoch) |
| Počas puzzle | `UI_Pozadie.png` | Štandardné pozadie |

## Testovanie

Pre testovanie dynamických pozadí:
1. Spustite Kapitolu 3
2. Prejdite cez úvodné dialógy - pozadie sa zmení na knižnicu
3. Vyriešte prvý hlavolam - pozadie sa vráti na knižnicu
4. Prejdite cez interlude dialógy - pozadie sa zmení na finálny obrázok knižnice
5. Vyriešte druhý hlavolam - pozadie zostane na finálnom obrázku

## Poznámky

- Implementácia sleduje rovnakú logiku ako Kapitoly 1 a 2
- Pozadia sa menia podľa naratívneho toku príbehu
- Puzzle používajú štandardné pozadie `UI_Pozadie.png`
- Zmeny sú konzistentné s existujúcim systémom dynamických pozadí
