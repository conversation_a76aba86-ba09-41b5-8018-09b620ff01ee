# Dynamické pozadia pre Kapitolu 4: <PERSON><PERSON><PERSON>

## Prehľad implementácie

Kapitola 4 teraz používa dynamické pozadia z priečinka `assets/pozadia/Kapitola_4/` podľa logiky príbehu a dialógov.

## Logika pozadí

### **Story Phase 0 (Úvod)**
- **Pozadie**: `1.png` 
- **Kontext**: "Staré krídlo pôsobí ako vstup do iného sveta. Vzduch je ťažší, studenší."
- **Dialógy**: Úvodné dialógy o starom krídle s mechanizmami, Viktor spomína básničku

### **Story Phase 1 (Po úvodných dialógoch)**
- **Pozadie**: `2.png`
- **Kontext**: Pred prvou hádankou (Pamäťový test)
- **Akcia**: Zobrazenie prvého hlavolamu

### **Story Phase 2 (Po prvom puzzle)**
- **Pozadie**: `3.png` (zmena pri vete "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami")
- **Kontext**: Interlude dialógy, vstup do laboratória
- **Dialógy**: "Úspech! Prešli ste bez spustenia mechanizmov" → "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami"

### **Story Phase 3 (Po interlude dialógoch)**
- **Pozadie**: `3.png` (zostáva v laboratóriu)
- **Kontext**: Pokračovanie v laboratóriu
- **Akcia**: Zobrazenie druhého hlavolamu (Vampírska aritmetika)

### **Počas puzzle**
- **Pozadie**: `UI_Pozadie.png` (štandardné pozadie pre všetky hlavolamy)

## Implementované zmeny

### **1. scenes/Chapter4.tscn**
```
[ext_resource type="Texture2D" path="res://assets/pozadia/Kapitola_4/1.png" id="2_bg"]
```
- Zmenené z `UI_Pozadie.png` na `1.png`
- Počiatočné pozadie je teraz prvý obrázok z Kapitola_4 (staré krídlo)

### **2. scripts/Chapter.gd**

**Zmena pozadia po úvodných dialógoch:**
```gdscript
# Po úvodných dialógoch - ukázať prvý hlavolam
story_phase = 1
# Pre Kapitolu 4 - zmeniť pozadie na druhý obrázok (pred prvou hádankou)
elif chapter_number == 4:
    change_background_image("res://assets/pozadia/Kapitola_4/2.png")
```

**Zmena pozadia počas interlude dialógov:**
```gdscript
# V DialogueSystem.gd - detekcia konkrétnej vety
elif text == "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami.":
    background_change_requested.emit("res://assets/pozadia/Kapitola_4/3.png")

# V Chapter.gd - spracovanie signálu
func _on_background_change_requested(image_path: String):
    change_background_image(image_path)
```

**Pozadie počas puzzle:**
```gdscript
func show_puzzle_scene(puzzle_number: int):
    # Pri spustení puzzle vrátiť pozadie na UI_Pozadie.png
    change_background_image("res://assets/Obrázky/UI_Pozadie.png")
```

**Návrat pozadia po prvom puzzle:**
```gdscript
func _on_memory_test_solved():
    # Pre kapitolu 4 - pokračovať v príbehu
    if chapter_number == 4:
        story_phase = 2
        hide_puzzle_buttons()
        # Vrátiť pozadie na 2.png (pozadie pred prvou hádankou)
        change_background_image("res://assets/pozadia/Kapitola_4/2.png")
        # Spustiť interlude dialógy (pozadie sa zmení automaticky pri konkrétnej vete)
        if dialogue_system:
            var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
            dialogue_system.start_dialogue(interlude_dialogue)
```

**Návrat pozadia po druhom puzzle:**
```gdscript
func _on_vampire_arithmetic_solved():
    # Pre kapitolu 4 - dokončiť kapitolu
    if chapter_number == 4:
        # Vrátiť pozadie na 3.png (pozadie pred druhou hádankou)
        change_background_image("res://assets/pozadia/Kapitola_4/3.png")
        complete_puzzle(2)
```

## Súhrn zmien pozadí

| Fáza príbehu | Pozadie | Popis |
|--------------|---------|-------|
| Story Phase 0 | `1.png` | Staré krídlo s mechanizmami |
| Story Phase 1 | `2.png` | Po úvodných dialógoch (pred prvou hádankou) |
| Story Phase 2 | `3.png` | Pri vete "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami" |
| Story Phase 3 | `3.png` | Zostáva v laboratóriu |
| Počas puzzle | `UI_Pozadie.png` | Štandardné pozadie |

## Automatický prechod do kapitoly 5

Kapitola 4 má implementované záverečné dialógy, ktoré sa spustia po dokončení druhého hlavolamu:
- Po vyriešení "Vampírska aritmetika" sa spustia záverečné dialógy o ochrannom elixíre
- Po dokončení záverečných dialógov sa zobrazí dialóg dokončenia kapitoly
- Po stlačení tlačidla "Ďalej" sa okamžite načíta Kapitola 5 (plynulý prechod)

## Testovanie

Pre testovanie dynamických pozadí:
1. Spustite Kapitolu 4
2. Prejdite cez úvodné dialógy - pozadie sa zmení na 2.png (pred prvou hádankou)
3. Vyriešte prvý hlavolam (Pamäťový test) - spustia sa interlude dialógy
4. Sledujte vetu "Laboratórium vyzerá ako miesto, kde sa zázraky miešajú s hrôzami" - pozadie sa zmení na laboratórium (3.png)
5. Dokončite interlude dialógy - pozadie zostane na 3.png
6. Vyriešte druhý hlavolam (Vampírska aritmetika) - spustia sa záverečné dialógy
7. Dokončite záverečné dialógy - automatický prechod na Kapitolu 5

## Poznámky

- Implementácia sleduje rovnakú logiku ako Kapitoly 1, 2 a 3
- Pozadia sa menia podľa naratívneho toku príbehu a konkrétnych viet v dialógoch
- Puzzle používajú štandardné pozadie `UI_Pozadie.png`
- Zmeny sú konzistentné s existujúcim systémom dynamických pozadí
- Zmena pozadia na laboratórium sa spúšťa presne pri vete o laboratóriu
